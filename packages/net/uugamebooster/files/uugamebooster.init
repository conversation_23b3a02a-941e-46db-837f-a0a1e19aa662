#!/bin/sh /etc/rc.common
# <AUTHOR> <EMAIL>

USE_PROCD=1
START=99

CONF="uugamebooster"
PROG="/usr/bin/uugamebooster"

UU_CONF="/usr/share/uugamebooster/uu.conf"

start_service() {
	config_load "$CONF"
	local enabled
	config_get_bool enabled "config" "enabled" "0"
	[ "$enabled" -eq "1" ] || return 1
	if [ ! -f /usr/bin/xtables-nft-multi ]; then
		cp /usr/sbin/xtables-nft-multi /usr/bin/xtables-nft-multi
	fi

	input_rule_exists=$(uci show firewall | grep -q "Allow-uugamebooster-Input" && echo 1 || echo 0)
	if [ "$input_rule_exists" -eq "0" ]; then
		uci add firewall rule
		uci set firewall.@rule[-1].name='Allow-uugamebooster-Input'
		uci set firewall.@rule[-1].src='*'
		uci set firewall.@rule[-1].target='ACCEPT'
		uci set firewall.@rule[-1].direction='in'
		uci set firewall.@rule[-1].device='tun163'
		uci add_list firewall.@rule[-1].proto='all'
	fi

	forward_rule_exists=$(uci show firewall | grep -q "Allow-uugamebooster-Forward-1" && echo 1 || echo 0)
	if [ "$forward_rule_exists" -eq "0" ]; then
		uci add firewall rule
		uci set firewall.@rule[-1].name='Allow-uugamebooster-Forward-1'
		uci set firewall.@rule[-1].src='*'
		uci set firewall.@rule[-1].dest='*'
		uci set firewall.@rule[-1].target='ACCEPT'
		uci set firewall.@rule[-1].direction='in'
		uci set firewall.@rule[-1].device='tun163'
		uci add_list firewall.@rule[-1].proto='all'
	fi

	forward_rule_exists=$(uci show firewall | grep -q "Allow-uugamebooster-Forward-2" && echo 1 || echo 0)
	if [ "$forward_rule_exists" -eq "0" ]; then
		uci add firewall rule
		uci set firewall.@rule[-1].name='Allow-uugamebooster-Forward-2'
		uci set firewall.@rule[-1].src='*'
		uci set firewall.@rule[-1].dest='*'
		uci set firewall.@rule[-1].target='ACCEPT'
		uci set firewall.@rule[-1].direction='out'
		uci set firewall.@rule[-1].device='tun163'
		uci add_list firewall.@rule[-1].proto='all'
	fi

	uci commit firewall
	/etc/init.d/firewall reload

	# Processing OpenClash firewall rules
	if [ -f "/etc/openclash/custom/openclash_custom_localnetwork_ipv4.list" ]; then
		if [ -n "$(tail -c1 /etc/openclash/custom/openclash_custom_localnetwork_ipv4.list)" ]; then
			echo >> /etc/openclash/custom/openclash_custom_localnetwork_ipv4.list
		fi
		sed -i '/# UU Game Booster Start/,/# UU Game Booster End/d' /etc/openclash/custom/openclash_custom_localnetwork_ipv4.list
		cat <<EOF >> /etc/openclash/custom/openclash_custom_localnetwork_ipv4.list
# UU Game Booster Start
***********/24
*************/24
*************/24
*************/24
# UU Game Booster End
EOF
		if [ -n "$(pgrep -f openclash)" ]; then
			/etc/init.d/openclash reload firewall
		fi
	fi

	procd_open_instance "$CONF"

	procd_set_param command "$PROG" "$UU_CONF"

	procd_set_param limits core="unlimited"
	procd_set_param respawn
	procd_set_param stdout 1
	procd_set_param stderr 1

	procd_close_instance
}

stop_service() {
	while uci show firewall | grep -q "Allow-uugamebooster-Input"; do
		rule_index=$(uci show firewall | grep -n "Allow-uugamebooster-Input" | head -n 1 | cut -d'[' -f2 | cut -d']' -f1)
		[ -n "$rule_index" ] && uci delete firewall.@rule[$rule_index]
	done
	while uci show firewall | grep -q "Allow-uugamebooster-Forward-1"; do
		rule_index=$(uci show firewall | grep -n "Allow-uugamebooster-Forward-1" | head -n 1 | cut -d'[' -f2 | cut -d']' -f1)
		[ -n "$rule_index" ] && uci delete firewall.@rule[$rule_index]
	done
	while uci show firewall | grep -q "Allow-uugamebooster-Forward-2"; do
		rule_index=$(uci show firewall | grep -n "Allow-uugamebooster-Forward-2" | head -n 1 | cut -d'[' -f2 | cut -d']' -f1)
		[ -n "$rule_index" ] && uci delete firewall.@rule[$rule_index]
	done
	uci commit firewall
	/etc/init.d/firewall reload

	# Processing OpenClash firewall rules
	if [ -f "/etc/openclash/custom/openclash_custom_localnetwork_ipv4.list" ]; then
		sed -i '/# UU Game Booster Start/,/# UU Game Booster End/d' /etc/openclash/custom/openclash_custom_localnetwork_ipv4.list
		if [ -n "$(pgrep -f openclash)" ]; then
			/etc/init.d/openclash reload firewall
		fi
	fi
}

reload_service() {
	stop
	start
}

service_triggers() {
	procd_add_reload_trigger "$CONF"
}

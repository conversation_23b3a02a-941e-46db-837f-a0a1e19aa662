#!/bin/sh /etc/rc.common
# Copyright (C) 2025

START=99
STOP=10

USE_PROCD=1

NAME=fakehttp
PROG=/usr/bin/fakehttp

start_service() {
	config_load fakehttp
	config_get_bool enabled main enabled 0

	# Check if service is enabled
	[ "$enabled" -eq 1 ] || return 1

	config_get host main host
	config_get iface main iface
	config_get mark main mark
	config_get num main num
	config_get repeat main repeat
	config_get ttl main ttl
	config_get mask main mask

	# Validate required parameters
	if [ -z "$iface" ]; then
		echo "Error: Network interface (iface) is required"
		return 1
	fi

	procd_open_instance
	procd_set_param command /usr/bin/fakehttp

	[ -n "$host" ] && procd_append_param command -h "$host"
	[ -n "$iface" ] && procd_append_param command -i "$iface"
	[ -n "$mark" ] && procd_append_param command -m "$mark"
	[ -n "$num" ] && procd_append_param command -n "$num"
	[ -n "$repeat" ] && procd_append_param command -r "$repeat"
	[ -n "$ttl" ] && procd_append_param command -t "$ttl"
	[ -n "$mask" ] && procd_append_param command -x "$mask"

	procd_append_param command -s

	procd_set_param respawn
	procd_set_param stdout 0
	procd_set_param stderr 0

	procd_close_instance
}

stop_service() {
	procd_kill fakehttp
}

reload_service() {
	stop
	start
}

service_triggers() {
	procd_add_reload_trigger "fakehttp"
}

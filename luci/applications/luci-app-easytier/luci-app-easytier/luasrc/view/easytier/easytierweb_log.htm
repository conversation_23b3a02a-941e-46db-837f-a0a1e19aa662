<%
local dsp = require "luci.dispatcher"
-%>

<script type="text/javascript">
	//<![CDATA[
	function clearlog(btn) {
		XHR.get('<%=dsp.build_url("admin/vpn/easytier/clear_wlog")%>', null,
			function(x, data) {
				if(x && x.status == 200) {
					var log_textarea = document.getElementById('log_textarea');
					log_textarea.innerHTML = "";
					log_textarea.scrollTop = log_textarea.scrollHeight;
				}
			}
		);
	}
	XHR.poll(2, '<%=dsp.build_url("admin/vpn/easytier/get_wlog")%>', null,
		function(x, data) {
			if(x && x.status == 200 && document.getElementById("checkbox1").checked == true) {
				var log_textarea = document.getElementById('log_textarea');
				log_textarea.innerHTML = x.responseText;
				log_textarea.scrollTop = log_textarea.scrollHeight;
			}
		}
	);
	//]]>
</script>
<fieldset class="cbi-section" id="_log_fieldset">
	<input type="checkbox" id="checkbox1" style="vertical-align:middle;height: auto;"checked><%:Auto Refresh%></input>
	<input class="cbi-button cbi-input-remove" type="button" onclick="clearlog()" value="<%:Clear Logs%>" />
	<textarea id="log_textarea" class="cbi-input-textarea" style="width: 100%;margin-top: 10px;" data-update="change" rows="30" wrap="off" readonly="readonly"></textarea>
</fieldset>

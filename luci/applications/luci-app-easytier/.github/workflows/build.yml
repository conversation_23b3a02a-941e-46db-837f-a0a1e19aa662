name: Build-OpenWrt-EasyTier

on:
  push:
  workflow_dispatch:
    inputs:
      tag:
        description: '请填写发布Releases的版本号（留空不发布）'
        required: false
        default: ''
      text:
        description: '请填写发布的说明'
        required: false
        default: ''
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
env:
  TAG: "${{ github.event.inputs.tag }}"
  TZ: Asia/Shanghai
permissions:
  contents: write
jobs:
  build:
    name: Build ${{ matrix.arch }}-${{ matrix.sdk }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        arch:
          - aarch64_cortex-a53
          - aarch64_cortex-a72
          - aarch64_generic
          - arm_arm1176jzf-s_vfp
          - arm_arm926ej-s
          - arm_cortex-a15_neon-vfpv4
          - arm_cortex-a5_vfpv4
          - arm_cortex-a7
          - arm_cortex-a7_neon-vfpv4
          - arm_cortex-a8_vfpv3
          - arm_cortex-a9
          - arm_cortex-a9_neon
          - arm_cortex-a9_vfpv3-d16
          - arm_fa526
          - arm_mpcore
          - arm_xscale
          - mips_24kc
          - mips_4kec
          - mips_mips32
          - mipsel_24kc
          - mipsel_24kc_24kf
          - mipsel_74kc
          - mipsel_mips32
          - x86_64
        sdk:
          - openwrt-22.03
          - SNAPSHOT

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Building packages
        uses: sbwml/openwrt-gh-action-sdk@main
        env:
          ARCH: ${{ matrix.arch }}-${{ matrix.sdk }}
          FEEDNAME: packages_ci
          PACKAGES: easytier luci-app-easytier
          NO_REFRESH_CHECK: true
          V: s

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: EasyTier-${{ matrix.arch }}-${{ matrix.sdk }}
          path: bin/packages/${{ matrix.arch }}/packages_ci/

      - name: Create compress files
        run: |
          #tar -zcvf EasyTier-${{ matrix.sdk }}-${{ matrix.arch }}.tar.gz -C bin/packages/${{ matrix.arch }}/ packages_ci
          zip -j EasyTier-${{ matrix.arch }}-${{ matrix.sdk }}.zip bin/packages/${{ matrix.arch }}/packages_ci/*

      - name: Release
        if: ${{ github.event.inputs.tag != '' }}
        uses: softprops/action-gh-release@v2
        with:
            token: ${{ secrets.GITHUB_TOKEN }}
            body: |

             - 其中 **`openwrt-22.03`** 里面是ipk安装包 **`SNAPSHOT`** 里面是apk安装包

             - **`*.apk` 是OpenWrt新版的APK包** 对于apk包跳过证书命令
             `apk add --allow-untrusted luci-app-easytier.apk`

             ${{ github.event.inputs.text }}
           
            tag_name: ${{ env.TAG }}
            files: EasyTier-${{ matrix.arch }}-${{ matrix.sdk }}.zip
 
